<?php

namespace Webkul\MLKWebAPI\Http\Controllers\API;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Webkul\Core\Repositories\CountryRepository;
use Webkul\Core\Repositories\CountryStateRepository;
use Webkul\Core\Repositories\LocaleRepository;
use Webkul\Category\Repositories\CategoryRepository;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Theme\Repositories\ThemeCustomizationRepository;

class CoreController extends APIController
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Core\Repositories\CountryRepository  $countryRepository
     * @param  \Webkul\Core\Repositories\CountryStateRepository  $countryStateRepository
     * @param  \Webkul\Core\Repositories\LocaleRepository  $localeRepository
     * @param  \Webkul\Category\Repositories\CategoryRepository  $categoryRepository
     * @param  \Webkul\Attribute\Repositories\AttributeRepository  $attributeRepository
     * @param  \Webkul\Theme\Repositories\ThemeCustomizationRepository  $themeCustomizationRepository
     * @return void
     */
    public function __construct(
        protected CountryRepository $countryRepository,
        protected CountryStateRepository $countryStateRepository,
        protected LocaleRepository $localeRepository,
        protected CategoryRepository $categoryRepository,
        protected AttributeRepository $attributeRepository,
        protected ThemeCustomizationRepository $themeCustomizationRepository
    ) {
    }

    /**
     * Get countries.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCountries()
    {
        return response()->json([
            'data' => core()->countries()->map(fn ($country) => [
                'id'   => $country->id,
                'code' => $country->code,
                'name' => $country->name,
            ]),
        ]);
    }

    /**
     * Get states.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStates()
    {
        return response()->json([
            'data' => core()->groupedStatesByCountries(),
        ]);
    }
    /**
     * Get locales/languages.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLocales()
    {
        // 获取默认渠道
        $channel = core()->getDefaultChannel();
        
        // 获取渠道支持的所有语言
        $locales = $channel->locales;
        
        // 获取渠道默认语言ID
        $defaultLocaleId = $channel->default_locale_id;
        
        // 为每个语言添加是否为默认语言的标记
        $locales->transform(function ($locale) use ($defaultLocaleId) {
            $locale->is_default = $locale->id == $defaultLocaleId;
            return $locale;
        });
        
        // 按名称排序
        $locales = $locales->sortBy('name')->values();

        return response()->json([
            'data' => $locales,
        ]);
    }

    /**
     * 获取所有根分类下的子分类
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCategories()
    {
        // 获取当前渠道的根分类ID
        $rootCategoryId = core()->getCurrentChannel()->root_category_id;
        
        // 获取根分类下的所有子分类树
        $categoryTree = $this->categoryRepository->getVisibleCategoryTree($rootCategoryId);
        
        // 转换分类为统一格式
        $formattedCategories = $this->formatCategories($categoryTree);
        
        return response()->json([
            'data' => $formattedCategories,
        ]);
    }

    public function getBrands()
    {
        // 获取请求参数
        $targetBrands = request()->post('brands', []);
       
        // 获取当前语言代码
        $currentLocale = app()->getLocale();
        
        // 查找brand属性
        $brandAttribute = $this->attributeRepository->findOneByField('code', 'brand');
        
        // 查找Device属性
        $deviceAttribute = $this->attributeRepository->findOneByField('code', 'device');
        
        if (!$brandAttribute) {
            return $this->success(['brands' => []]);
        }
        
        // 获取品牌选项的查询构建器
        $brandQuery = $brandAttribute->options()->with(['translations'])->orderBy('sort_order');
        
        // 如果指定了目标品牌，则过滤品牌
        if (!empty($targetBrands)) {
            $targetBrands = json_decode($targetBrands, true);
            $brandQuery->whereHas('translations', function ($query) use ($targetBrands) {
                $query->where(function ($subQuery) use ($targetBrands) {
                    foreach ($targetBrands as $brand) {
                        $subQuery->orWhere('label', 'like', '%' . $brand . '%');
                    }
                });
            })->orWhere(function ($query) use ($targetBrands) {
                foreach ($targetBrands as $brand) {
                    $query->orWhere('admin_name', 'like', '%' . $brand . '%');
                }
            });
        }
        
        $brandOptions = $brandQuery->get();
        
        // 获取设备选项（如果存在device属性）
        $deviceOptions = collect();
        if ($deviceAttribute) {
            $deviceOptions = $deviceAttribute->options()->with(['translations'])->orderBy('sort_order')->get();
        }
        
        // 构建品牌数据并关联对应的设备
        $brands = $brandOptions->map(function ($brandOption) use ($currentLocale, $deviceOptions) {
            // 查找属于这个品牌的设备
            $relatedDevices = $deviceOptions->filter(function ($deviceOption) use ($brandOption) {
                return $deviceOption->parent_id == $brandOption->id;
            });
            
            $relatedDevices = $relatedDevices->map(function ($deviceOption) use ($currentLocale) {
                return [
                    'id' => $deviceOption->id,
                    'label' => $deviceOption->translate($currentLocale)?->label ?? $deviceOption->admin_name,
                    'sort_order' => $deviceOption->sort_order,
                    // 'swatch_value' => $deviceOption->swatch_value,
                    // 'swatch_value_url' => Storage::url($deviceOption->swatch_value),
                ];
            })->values();
            
            return [
                'id' => $brandOption->id,
                'label' => $brandOption->translate($currentLocale)?->label ?? $brandOption->admin_name,
                'sort_order' => $brandOption->sort_order,
                'swatch_value' => $brandOption->swatch_value,
                'swatch_value_url' => Storage::url($brandOption->swatch_value),
                'devices' => $relatedDevices,
            ];
        });
        
        return $this->success(['brands' => $brands]);
    }

    /**
     * 将分类转换为统一格式
     *
     * @param array $categories
     * @param int $position
     * @return array
     */
    protected function formatCategories($categories, $startPosition = 1)
    {
        $formattedCategories = [];
        $position = $startPosition;
        
        foreach ($categories as $category) {
            $formattedCategory = [
                'name' => $category->name,
                'slug' => $category->slug,
                'position' => $position++,
                'logo_path' => $category->logo_url ?? '',
                'category_ids' => [$category->id],
                'children' => [],
            ];
            
            // 递归处理子分类
            if (!empty($category->children)) {
                $formattedCategory['children'] = $this->formatCategories($category->children, 1);
            }
            
            $formattedCategories[] = $formattedCategory;
        }
        
        return $formattedCategories;
    }

    /**
     * 获取导航栏
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNavbar()
    {
        // 获取当前语言环境
        $locale = app()->getLocale();
        
        // 获取当前渠道的根分类ID
        $rootCategoryId = core()->getCurrentChannel()->root_category_id;
        
        // // 获取根分类下的所有子分类树
        $categoryTree = $this->categoryRepository->getVisibleCategoryTree($rootCategoryId);
        
        // // 转换分类为统一格式
        $formattedCategories = $this->formatCategories($categoryTree);
       
        // $navbar = [
        //     [
        //         'name' => trans('mlk::app.api.navbar.devices'),
        //         'category_ids' => [],
        //         'position' => 1,
        //         'logo_path' => '',
        //         'children' => []
        //     ],
        //     [
        //         'name' => trans('mlk::app.api.navbar.case_and_accessories'),
        //         'category_ids' => [4,8],
        //         'position' => 2,
        //         'logo_path' => '',
        //         'children' => []
        //     ],
        //     [
        //         'name' => trans('mlk::app.api.navbar.audio'),
        //         'category_ids' => [9],
        //         'position' => 3,
        //         'logo_path' => '',
        //         'children' => []
        //     ],
        //     [
        //         'name' => trans('mlk::app.api.navbar.power_and_cables'),
        //         'category_ids' => [10,11],
        //         'position' => 4,
        //         'logo_path' => '',
        //         'children' => []
        //     ],
        //     [
        //         'name' => trans('mlk::app.api.navbar.phone_holders'),
        //         'category_ids' => [12],
        //         'position' => 5,
        //         'logo_path' => '',
        //         'children' => []
        //     ]
        // ];
        return $this->success($formattedCategories);
    }

    /**
     * 获取header_offer配置
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHeaderOffer()
    {
        // 获取当前渠道代码
        $currentChannelCode = core()->getCurrentChannel()->code;
        
        // 获取当前语言代码
        $currentLocaleCode = app()->getLocale();
        
        // 获取header_offer相关的三个配置值
        $title = core()->getConfigData('general.content.header_offer.title', $currentChannelCode, $currentLocaleCode) ?? '';
        $redirectionTitle = core()->getConfigData('general.content.header_offer.redirection_title', $currentChannelCode, $currentLocaleCode) ?? '';
        $redirectionLink = core()->getConfigData('general.content.header_offer.redirection_link', $currentChannelCode, $currentLocaleCode) ?? '';
        
        return $this->success([
            'title' => $title,
            'redirection_title' => $redirectionTitle,
            'redirection_link' => $redirectionLink,
        ]);
    }

    /**
     * 获取底部页面内容
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFooter()
    {
        // 获取当前渠道代码
        $currentChannelCode = core()->getCurrentChannel()->code;
        
        // 获取当前语言代码
        $currentLocaleCode = app()->getLocale();

        // 检查用户是否为批发客户
        $isWholesale = false;
        $token = request()->bearerToken();
        if ($token) {
            $accessToken = \Laravel\Sanctum\PersonalAccessToken::findToken($token);
            if ($accessToken && $accessToken->tokenable) {
                $user = $accessToken->tokenable;
                if (isset($user->customer_group_id)) {
                    $customerGroup = app(\Webkul\Customer\Repositories\CustomerGroupRepository::class)->find($user->customer_group_id);
                    if ($customerGroup && $customerGroup->code === 'wholesale') {
                        $isWholesale = true;
                    }
                }
            }
        }

        // 获取footer_links
        $footer_links = $this->getThemeCustomization('footer_links', $currentLocaleCode, $isWholesale);
        
        return $this->success([
            'footer_links' => $footer_links,
        ]);
    }

    /**
     * 获取主题自定义
     *
     * @param string $name
     * @param string $locale
     * @param bool $isWholesale
     * @return array
     */
    protected function getThemeCustomization($name, $locale, $isWholesale)
    {
        // 根据是否批发客户确定查询的主题名称
        $themeName = $isWholesale ? 'wholesale_'.$name : $name;
        
        // 获取当前渠道ID
        $channelId = core()->getCurrentChannel()->id;

        // 查询主题自定义表
        $themeCustomization = $this->themeCustomizationRepository->findWhere([
            'name' => $themeName,
            'channel_id' => $channelId,
            'status' => 1
        ])->first();
      
        if (!$themeCustomization) {
            return [];
        }
       
        // 获取当前语言的翻译
        $translation = $themeCustomization->translate($locale);
        // 如果当前语言没有翻译，则使用en翻译
        if (!$translation) {
            $translation = $themeCustomization->translate('en');
        }        
        return $translation ? $translation->options : [];
    }
} 