{"__meta": {"id": "01K1AM7DE9C5E8CVYQTGEBKKR2", "datetime": "2025-07-29 09:19:17", "utime": **********.578408, "method": "POST", "uri": "/api/mlk/common/brands", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (2)", "Webkul\\Attribute\\Models\\AttributeOption (124)", "Webkul\\Attribute\\Models\\AttributeOptionTranslation (620)"], "views": [], "queries": [{"sql": "select * from `attributes` where `code` = 'brand'", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'device'", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (371, 372, 373, 374, 375, 376, 377, 378, 379, 380)", "duration": 1.83, "duration_str": "1.83s", "connection": "mlk"}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 33 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "duration": 0.45, "duration_str": "450ms", "connection": "mlk"}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494)", "duration": 1.03, "duration_str": "1.03s", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (6)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.98, "duration_str": "1.98s", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}]}, "messages": {"count": 117, "messages": [{"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.443155, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.444423, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.445494, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.446792, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.447937, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.44908, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.450093, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.451052, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.452, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.452982, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.453916, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.454845, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.455776, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.456726, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.457644, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.458595, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.459579, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.460558, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.461541, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.464575, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.465489, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.466418, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.467357, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.468294, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.469272, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.470215, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.47112, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.472055, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.472993, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.473958, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.474932, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.475873, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.47688, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.477863, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.480856, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.481811, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.482743, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.483677, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.484588, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.4855, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.486403, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.487315, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.488238, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.489139, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.492237, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.493176, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.49412, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.495027, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.496, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.496918, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.497842, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.498792, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.49972, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.500665, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.503673, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.504596, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.505507, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.506428, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.507344, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.508426, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.509455, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.510423, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.511363, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.512291, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.515348, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.5163, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.517228, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.518157, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.519139, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.520067, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.521071, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.521989, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.522977, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.523898, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.527062, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.528075, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.529021, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.529978, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.530917, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.531882, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.532804, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.533707, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.534604, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.535547, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.538458, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.539392, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.540335, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.541274, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.542215, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.543167, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.544175, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.545102, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.546083, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.547024, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.547951, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.550042, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.550968, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.551897, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.552834, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.553799, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.554727, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.555656, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.5566, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.557506, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.558449, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.559385, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.561425, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.562329, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.563242, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.564189, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.565113, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.566104, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.567055, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.567986, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.569079, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.570137, "xdebug_link": null, "collector": "log"}, {"message": "[09:19:17] LOG.warning: ltrim(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemAdapter.php on line 850", "message_html": null, "is_string": false, "label": "warning", "time": **********.571099, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.104071, "end": **********.585172, "duration": 0.48110103607177734, "duration_str": "481ms", "measures": [{"label": "Booting", "start": **********.104071, "relative_start": 0, "end": **********.366287, "relative_end": **********.366287, "duration": 0.*****************, "duration_str": "262ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.3663, "relative_start": 0.*****************, "end": **********.585174, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.38068, "relative_start": 0.****************, "end": **********.384062, "relative_end": **********.384062, "duration": 0.003381967544555664, "duration_str": "3.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.576491, "relative_start": 0.*****************, "end": **********.576823, "relative_end": **********.576823, "duration": 0.000331878662109375, "duration_str": "332μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.576843, "relative_start": 0.*****************, "end": **********.576856, "relative_end": **********.576856, "duration": 1.2874603271484375e-05, "duration_str": "13μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "it"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 9, "nb_statements": 9, "nb_visible_statements": 9, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00996, "accumulated_duration_str": "9.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}], "start": **********.398276, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 20.382}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 31}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.404862, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 20.382, "width_percent": 19.88}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": "middleware", "name": "api_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Middleware\\LocaleMiddleware.php", "line": 52}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 127}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 807}], "start": **********.409007, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 40.261, "width_percent": 1.908}, {"sql": "select * from `attributes` where `code` = 'brand'", "type": "query", "params": [], "bindings": ["brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4103289, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 42.169, "width_percent": 2.309}, {"sql": "select * from `attributes` where `code` = 'device'", "type": "query", "params": [], "bindings": ["device"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 127}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.412042, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 44.478, "width_percent": 1.908}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 25 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 152}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4139261, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "CoreController.php:152", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=152", "ajax": false, "filename": "CoreController.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 46.386, "width_percent": 20.382}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (371, 372, 373, 374, 375, 376, 377, 378, 379, 380)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 152}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.4171648, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "CoreController.php:152", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=152", "ajax": false, "filename": "CoreController.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 66.767, "width_percent": 18.373}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` = 33 and `attribute_options`.`attribute_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 157}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.420247, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CoreController.php:157", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=157", "ajax": false, "filename": "CoreController.php", "line": "157"}, "connection": "mlk", "explain": null, "start_percent": 85.141, "width_percent": 4.518}, {"sql": "select * from `attribute_option_translations` where `attribute_option_translations`.`attribute_option_id` in (381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 157}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.42293, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "CoreController.php:157", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\MLKWebAPI\\src\\Http\\Controllers\\API\\CoreController.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=157", "ajax": false, "filename": "CoreController.php", "line": "157"}, "connection": "mlk", "explain": null, "start_percent": 89.659, "width_percent": 10.341}]}, "models": {"data": {"Webkul\\Attribute\\Models\\AttributeOptionTranslation": {"value": 620, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOptionTranslation.php&line=1", "ajax": false, "filename": "AttributeOptionTranslation.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeOption": {"value": 124, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeOption.php&line=1", "ajax": false, "filename": "AttributeOption.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}}, "count": 753, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/common/brands", "action_name": "mlk.api.core.brands", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CoreController@getBrands", "uri": "POST api/mlk/common/brands", "controller": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CoreController@getBrands<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=115\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/mlk/common", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FMLKWebAPI%2Fsrc%2FHttp%2FControllers%2FAPI%2FCoreController.php&line=115\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/MLKWebAPI/src/Http/Controllers/API/CoreController.php:115-188</a>", "middleware": "api_locale, api_auth", "duration": "483ms", "peak_memory": "38MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-426118357 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-426118357\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914845902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914845902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1901573227 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">gzip, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Apifox/1.0.0 (https://apifox.com)</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901573227\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-8631336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-8631336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-836819853 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 08:19:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-836819853\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-642903691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-642903691\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/api/mlk/common/brands", "action_name": "mlk.api.core.brands", "controller_action": "Webkul\\MLKWebAPI\\Http\\Controllers\\API\\CoreController@getBrands"}, "badge": null}}